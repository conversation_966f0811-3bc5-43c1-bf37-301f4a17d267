{
    "folders": [
		{
			"name": "RT-Thread MicroPython",
			"path": "."
		}
	],
    "settings": {
        "files.associations": {
            ".mpyproject.json": "jsonc"
        },

        /* 自动补全/跳转 */
        "python.autoComplete.extraPaths": [
            "D:/Micro Python/micropython-1.25.0/lib/micropython-lib/micropython",
            "c:/Users/<USER>/.vscode/extensions/rt-thread.rt-thread-micropython-1.0.11/microExamples/code-completion"
        ],

        /* IntelliSense */
        "python.analysis.extraPaths": [
            "D:/Micro Python/micropython-1.25.0/lib/micropython-lib/micropython",
            "c:/Users/<USER>/.vscode/extensions/rt-thread.rt-thread-micropython-1.0.11/microExamples/code-completion"
        ],

        /* 静态检查（Pylint） */
        "python.linting.pylintEnabled": true,
        "python.linting.pylintArgs": [
            "--init-hook",
            "import sys; sys.path.append('D:/Micro Python/micropython-1.25.0/lib/micropython-lib/micropython'); sys.path.append('c:/Users/<USER>/.vscode/extensions/rt-thread.rt-thread-micropython-1.0.11/microExamples/code-completion')"
        ]
    }
}